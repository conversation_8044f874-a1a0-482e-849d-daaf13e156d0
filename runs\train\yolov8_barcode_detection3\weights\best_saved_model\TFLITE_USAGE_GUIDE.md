# TensorFlow Lite Model Usage Guide

## Model Information
- **File**: `runs/train/yolov8_barcode_detection3/weights/best_saved_model/best_float32.tflite`
- **Size**: 42.8 MB
- **Task**: Barcode Detection
- **Input**: RGB images (640x640 pixels)
- **Output**: Bounding boxes and class predictions

## Python Usage Example

```python
import tensorflow as tf
import numpy as np
from PIL import Image

# Load TensorFlow Lite model
interpreter = tf.lite.Interpreter(model_path='runs/train/yolov8_barcode_detection3/weights/best_saved_model/best_float32.tflite')
interpreter.allocate_tensors()

# Get input and output details
input_details = interpreter.get_input_details()
output_details = interpreter.get_output_details()

# Load and preprocess image
image = Image.open('your_image.jpg').convert('RGB')
image = image.resize((640, 640))
input_array = np.array(image, dtype=np.float32) / 255.0
input_array = np.expand_dims(input_array, axis=0)  # Add batch dimension

# Run inference
interpreter.set_tensor(input_details[0]['index'], input_array)
interpreter.invoke()

# Get results
output_data = interpreter.get_tensor(output_details[0]['index'])
print("Detection results:", output_data.shape)
```

## Mobile Integration
- **Android**: Use TensorFlow Lite Android library
- **iOS**: Use TensorFlow Lite iOS library
- **Flutter**: Use tflite_flutter plugin

## Performance Tips
1. Use GPU delegate for mobile acceleration
2. Consider quantized model for smaller size
3. Optimize input preprocessing pipeline
4. Use appropriate confidence thresholds
