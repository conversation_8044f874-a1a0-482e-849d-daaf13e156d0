#!/usr/bin/env python3
"""
YOLOv8 Barcode Detection Inference Test Script
Test your trained YOLOv8s model on images
"""

import torch
from pathlib import Path
import loguru
from ultralytics import YOLO
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# Import barcode decoding libraries with fallbacks
AVAILABLE_DECODERS = []

try:
    from pyzbar import pyzbar

    AVAILABLE_DECODERS.append("pyzbar")
    loguru.logger.info("✅ pyzbar available")
except ImportError:
    loguru.logger.warning("❌ pyzbar not available")

try:
    import zxingcpp

    AVAILABLE_DECODERS.append("zxingcpp")
    loguru.logger.info("✅ zxing-cpp available")
except ImportError:
    loguru.logger.warning("❌ zxing-cpp not available")

try:
    # OpenCV barcode detector (available in opencv-contrib-python)
    cv2_detector = cv2.barcode.BarcodeDetector()
    AVAILABLE_DECODERS.append("opencv")
    loguru.logger.info("✅ OpenCV barcode detector available")
except AttributeError:
    loguru.logger.warning(
        "❌ OpenCV barcode detector not available (need opencv-contrib-python)"
    )

if not AVAILABLE_DECODERS:
    loguru.logger.error(
        "❌ No barcode decoders available! Please install pyzbar, zxing-cpp, or opencv-contrib-python"
    )
else:
    loguru.logger.info(f"📚 Available decoders: {', '.join(AVAILABLE_DECODERS)}")


def load_model(model_path: str = None):
    """Load the trained YOLOv8 model"""
    if model_path is None:
        # Find the latest training run
        runs_dir = Path("runs/train")
        yolo_runs = [
            d
            for d in runs_dir.iterdir()
            if d.is_dir() and "yolov8_barcode_detection" in d.name
        ]

        if not yolo_runs:
            loguru.logger.error("No YOLOv8 training runs found!")
            return None

        # Get the latest run (highest number)
        latest_run = sorted(yolo_runs, key=lambda x: x.name)[-1]
        model_path = latest_run / "weights" / "best.pt"

    if not Path(model_path).exists():
        loguru.logger.error(f"Model not found at {model_path}")
        return None

    loguru.logger.info(f"Loading model from: {model_path}")
    model = YOLO(str(model_path))
    return model


def decode_with_pyzbar(processed_roi):
    """Try decoding with pyzbar"""
    if "pyzbar" not in AVAILABLE_DECODERS:
        return None

    try:
        decoded_objects = pyzbar.decode(processed_roi)
        if decoded_objects:
            obj = decoded_objects[0]
            return {
                "type": str(obj.type),
                "data": obj.data.decode("utf-8") if obj.data else None,
                "library": "pyzbar",
            }
    except Exception:
        pass
    return None


def decode_with_zxingcpp(processed_roi):
    """Try decoding with zxing-cpp"""
    if "zxingcpp" not in AVAILABLE_DECODERS:
        return None

    try:
        # zxing-cpp expects grayscale images
        if len(processed_roi.shape) == 3:
            processed_roi = cv2.cvtColor(processed_roi, cv2.COLOR_RGB2GRAY)

        results = zxingcpp.read_barcodes(processed_roi)
        if results:
            result = results[0]
            return {
                "type": str(result.format),
                "data": result.text,
                "library": "zxingcpp",
            }
    except Exception:
        pass
    return None


def decode_with_opencv(processed_roi):
    """Try decoding with OpenCV barcode detector"""
    if "opencv" not in AVAILABLE_DECODERS:
        return None

    try:
        # OpenCV expects grayscale images
        if len(processed_roi.shape) == 3:
            processed_roi = cv2.cvtColor(processed_roi, cv2.COLOR_RGB2GRAY)

        retval, decoded_info, decoded_type, points = cv2_detector.detectAndDecode(
            processed_roi
        )
        if retval and decoded_info:
            return {
                "type": str(decoded_type[0]) if decoded_type else "Unknown",
                "data": decoded_info[0],
                "library": "opencv",
            }
    except Exception:
        pass
    return None


def decode_barcode_region(image, x1, y1, x2, y2):
    """
    Decode barcode content from a specific region using multiple libraries and preprocessing methods

    Args:
        image: Original image (BGR format from OpenCV)
        x1, y1, x2, y2: Bounding box coordinates

    Returns:
        dict: Contains 'type', 'data', 'success' keys
    """
    try:
        # Add padding to the region to ensure we capture the full barcode
        padding = 15  # Increased padding
        h, w = image.shape[:2]
        x1_padded = max(0, int(x1) - padding)
        y1_padded = max(0, int(y1) - padding)
        x2_padded = min(w, int(x2) + padding)
        y2_padded = min(h, int(y2) + padding)

        # Extract the region of interest with padding
        roi = image[y1_padded:y2_padded, x1_padded:x2_padded]

        if roi.size == 0:
            return {"type": None, "data": None, "success": False, "error": "Empty ROI"}

        # Convert BGR to RGB for some decoders
        roi_rgb = cv2.cvtColor(roi, cv2.COLOR_BGR2RGB)
        roi_gray = cv2.cvtColor(roi_rgb, cv2.COLOR_RGB2GRAY)

        # Create multiple preprocessing variations
        preprocessing_methods = [
            ("original_rgb", roi_rgb),
            ("original_gray", roi_gray),
        ]

        # Enhanced preprocessing methods
        # Method 1: Histogram equalization
        roi_hist_eq = cv2.equalizeHist(roi_gray)
        preprocessing_methods.append(("histogram_equalized", roi_hist_eq))

        # Method 2: Gaussian blur to reduce noise
        roi_blurred = cv2.GaussianBlur(roi_gray, (3, 3), 0)
        preprocessing_methods.append(("gaussian_blur", roi_blurred))

        # Method 3: Adaptive threshold
        roi_adaptive = cv2.adaptiveThreshold(
            roi_gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        preprocessing_methods.append(("adaptive_threshold", roi_adaptive))

        # Method 4: Otsu's threshold
        _, roi_otsu = cv2.threshold(
            roi_gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
        )
        preprocessing_methods.append(("otsu_threshold", roi_otsu))

        # Method 5: Inverted Otsu's threshold
        _, roi_otsu_inv = cv2.threshold(
            roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU
        )
        preprocessing_methods.append(("otsu_threshold_inv", roi_otsu_inv))

        # Method 6: Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        roi_morph = cv2.morphologyEx(roi_otsu, cv2.MORPH_CLOSE, kernel)
        preprocessing_methods.append(("morphological", roi_morph))

        # Method 7: Resize if the region is small
        if roi.shape[0] < 100 or roi.shape[1] < 100:
            scale_factor = 3
            roi_resized = cv2.resize(
                roi_gray,
                None,
                fx=scale_factor,
                fy=scale_factor,
                interpolation=cv2.INTER_CUBIC,
            )
            preprocessing_methods.append(("resized", roi_resized))

        # Define decoder functions to try
        decoder_functions = [
            decode_with_zxingcpp,  # Try zxing-cpp first (often more robust)
            decode_with_opencv,  # Then OpenCV
            decode_with_pyzbar,  # Finally pyzbar
        ]

        # Try each decoder with each preprocessing method
        for decoder_func in decoder_functions:
            for method_name, processed_roi in preprocessing_methods:
                result = decoder_func(processed_roi)
                if result:
                    return {
                        "type": result["type"],
                        "data": result["data"],
                        "success": True,
                        "error": None,
                        "method": f"{method_name}_{result['library']}",
                        "library": result["library"],
                    }

        # Try with different rotation angles as a last resort
        for decoder_func in decoder_functions:
            for angle in [90, 180, 270]:
                try:
                    # Rotate the grayscale image
                    center = (roi_gray.shape[1] // 2, roi_gray.shape[0] // 2)
                    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
                    rotated = cv2.warpAffine(
                        roi_gray,
                        rotation_matrix,
                        (roi_gray.shape[1], roi_gray.shape[0]),
                    )

                    result = decoder_func(rotated)
                    if result:
                        return {
                            "type": result["type"],
                            "data": result["data"],
                            "success": True,
                            "error": None,
                            "method": f"rotated_{angle}_{result['library']}",
                            "library": result["library"],
                        }
                except Exception:
                    continue

        return {
            "type": None,
            "data": None,
            "success": False,
            "error": f"No barcode found after trying {len(preprocessing_methods)} methods with {len([d for d in decoder_functions if d.__name__.split('_')[-1] in AVAILABLE_DECODERS])} decoders",
        }

    except Exception as e:
        return {"type": None, "data": None, "success": False, "error": str(e)}


def save_debug_regions(image, boxes, save_dir="debug_regions"):
    """
    Save extracted barcode regions for debugging purposes

    Args:
        image: Original image
        boxes: Detection boxes
        save_dir: Directory to save debug images
    """
    import os

    # Create debug directory if it doesn't exist
    os.makedirs(save_dir, exist_ok=True)

    for i, box in enumerate(boxes):
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()

        # Add padding
        padding = 10
        h, w = image.shape[:2]
        x1_padded = max(0, int(x1) - padding)
        y1_padded = max(0, int(y1) - padding)
        x2_padded = min(w, int(x2) + padding)
        y2_padded = min(h, int(y2) + padding)

        # Extract region
        roi = image[y1_padded:y2_padded, x1_padded:x2_padded]

        if roi.size > 0:
            # Save original region
            cv2.imwrite(f"{save_dir}/region_{i+1}_original.jpg", roi)

            # Save grayscale version
            roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            cv2.imwrite(f"{save_dir}/region_{i+1}_grayscale.jpg", roi_gray)

            # Save thresholded version
            _, roi_thresh = cv2.threshold(
                roi_gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
            )
            cv2.imwrite(f"{save_dir}/region_{i+1}_threshold.jpg", roi_thresh)

    loguru.logger.info(f"Saved {len(boxes)} debug regions to '{save_dir}' directory")


def predict_image(
    model,
    image_path: str,
    conf_threshold: float = 0.25,
    save_results: bool = True,
    decode_barcodes: bool = True,
):
    """Run inference on an image and optionally decode detected barcodes"""

    if not Path(image_path).exists():
        loguru.logger.error(f"Image not found: {image_path}")
        return None

    loguru.logger.info(f"Running inference on: {image_path}")

    # Run prediction
    results = model(
        image_path,
        conf=conf_threshold,
        save=save_results,
        project="runs/detect",
        name="test_results",
    )

    result = results[0]  # Get first result

    # Add barcode decoding if requested and detections exist
    if decode_barcodes and result.boxes is not None and len(result.boxes) > 0:
        loguru.logger.info("Decoding detected barcodes...")

        # Load the original image for decoding
        original_image = cv2.imread(image_path)

        # Save debug regions for inspection
        save_debug_regions(original_image, result.boxes)

        # Add decoded data to each detection
        decoded_results = []
        for i, box in enumerate(result.boxes):
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
            conf = box.conf[0].cpu().numpy()
            cls = int(box.cls[0].cpu().numpy())

            # Decode barcode in this region
            decode_result = decode_barcode_region(original_image, x1, y1, x2, y2)

            # Create enhanced detection result
            detection_data = {
                "bbox": [x1, y1, x2, y2],
                "confidence": conf,
                "class_id": cls,
                "decoded_type": decode_result["type"],
                "decoded_data": decode_result["data"],
                "decode_success": decode_result["success"],
                "decode_error": decode_result["error"],
            }
            decoded_results.append(detection_data)

            if decode_result["success"]:
                method_info = (
                    f" (method: {decode_result.get('method', 'unknown')})"
                    if "method" in decode_result
                    else ""
                )
                loguru.logger.info(
                    f"Detection {i+1}: Decoded {decode_result['type']} = '{decode_result['data']}'{method_info}"
                )
            else:
                loguru.logger.warning(
                    f"Detection {i+1}: Failed to decode - {decode_result['error']}"
                )

        # Store decoded results in the result object for later use
        result.decoded_results = decoded_results

    return result


def visualize_results(result, class_names=None):
    """Create a detailed visualization of detection results"""

    # Get the original image
    img = result.orig_img.copy()

    if result.boxes is not None and len(result.boxes) > 0:
        boxes = result.boxes

        # Convert BGR to RGB for matplotlib
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))
        ax.imshow(img_rgb)

        # Default class names if not provided
        if class_names is None:
            class_names = [
                "EAN13",
                "PDF417",
                "DataMatrix",
                "QRCode",
                "RoyalMailCode",
                "Kix",
                "Code128",
                "UPCA",
                "Aztec",
                "Interleaved25",
                "JapanPost",
                "Code39",
                "Postnet",
                "UCC128",
                "IntelligentMail",
                "2-Digit",
                "EAN8",
                "IATA25",
            ]

        # Draw boxes and labels
        for i, box in enumerate(boxes):
            # Get box coordinates
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
            conf = box.conf[0].cpu().numpy()
            cls = int(box.cls[0].cpu().numpy())

            # Create rectangle
            rect = patches.Rectangle(
                (x1, y1),
                x2 - x1,
                y2 - y1,
                linewidth=2,
                edgecolor="red",
                facecolor="none",
            )
            ax.add_patch(rect)

            # Add label with detection info
            class_name = class_names[cls] if cls < len(class_names) else f"Class_{cls}"
            label = f"{class_name}: {conf:.2f}"

            # Add decoded barcode information if available
            if hasattr(result, "decoded_results") and i < len(result.decoded_results):
                decoded_info = result.decoded_results[i]
                if decoded_info["decode_success"]:
                    decoded_type = decoded_info["decoded_type"]
                    decoded_data = decoded_info["decoded_data"]
                    # Truncate long decoded data for display
                    if decoded_data and len(decoded_data) > 30:
                        decoded_data = decoded_data[:30] + "..."
                    label += f"\nDecoded: {decoded_type}\nData: {decoded_data}"
                else:
                    label += f"\nDecode: Failed"

            ax.text(
                x1,
                y1 - 5,
                label,
                fontsize=10,
                color="red",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                verticalalignment="top",
            )

        ax.set_title(
            f"YOLOv8s Barcode Detection - Found {len(boxes)} barcodes", fontsize=16
        )
        ax.axis("off")

        # Save the visualization
        plt.tight_layout()
        output_path = "test_results_visualization.jpg"
        plt.savefig(output_path, dpi=150, bbox_inches="tight")
        loguru.logger.info(f"Visualization saved to: {output_path}")
        plt.show()

        return len(boxes)
    else:
        loguru.logger.warning("No detections found!")
        return 0


def print_detection_summary(result):
    """Print detailed detection information"""

    if result.boxes is not None and len(result.boxes) > 0:
        boxes = result.boxes

        class_names = [
            "EAN13",
            "PDF417",
            "DataMatrix",
            "QRCode",
            "RoyalMailCode",
            "Kix",
            "Code128",
            "UPCA",
            "Aztec",
            "Interleaved25",
            "JapanPost",
            "Code39",
            "Postnet",
            "UCC128",
            "IntelligentMail",
            "2-Digit",
            "EAN8",
            "IATA25",
        ]

        loguru.logger.info("=== DETECTION RESULTS ===")
        loguru.logger.info(f"Total detections: {len(boxes)}")

        # Count detections by class and show decoded information
        class_counts = {}
        decoded_counts = {"successful": 0, "failed": 0}

        for i, box in enumerate(boxes):
            conf = box.conf[0].cpu().numpy()
            cls = int(box.cls[0].cpu().numpy())
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()

            class_name = class_names[cls] if cls < len(class_names) else f"Class_{cls}"

            if class_name not in class_counts:
                class_counts[class_name] = 0
            class_counts[class_name] += 1

            # Basic detection info
            detection_info = f"Detection {i+1}: {class_name} (conf: {conf:.3f}) at [{x1:.0f}, {y1:.0f}, {x2:.0f}, {y2:.0f}]"

            # Add decoded information if available
            if hasattr(result, "decoded_results") and i < len(result.decoded_results):
                decoded_info = result.decoded_results[i]
                if decoded_info["decode_success"]:
                    decoded_counts["successful"] += 1
                    decoded_type = decoded_info["decoded_type"]
                    decoded_data = decoded_info["decoded_data"]
                    detection_info += f" | Decoded: {decoded_type} = '{decoded_data}'"
                else:
                    decoded_counts["failed"] += 1
                    detection_info += (
                        f" | Decode failed: {decoded_info['decode_error']}"
                    )

            loguru.logger.info(detection_info)

        loguru.logger.info("\n=== SUMMARY BY TYPE ===")
        for class_name, count in sorted(class_counts.items()):
            loguru.logger.info(f"{class_name}: {count} detections")

        # Add decoding summary if decoded results are available
        if hasattr(result, "decoded_results"):
            loguru.logger.info("\n=== BARCODE DECODING SUMMARY ===")
            loguru.logger.info(f"Successfully decoded: {decoded_counts['successful']}")
            loguru.logger.info(f"Failed to decode: {decoded_counts['failed']}")
            success_rate = (
                (decoded_counts["successful"] / len(boxes)) * 100
                if len(boxes) > 0
                else 0
            )
            loguru.logger.info(f"Decoding success rate: {success_rate:.1f}%")

    else:
        loguru.logger.info("No barcodes detected in the image")


def main():
    """Main testing function"""

    loguru.logger.info("YOLOv8s Barcode Detection - Inference Test")

    # Check GPU availability
    if torch.cuda.is_available():
        loguru.logger.info(f"GPU available: {torch.cuda.get_device_name(0)}")
    else:
        loguru.logger.info("Using CPU for inference")

    # Load the trained model
    model = load_model()
    if model is None:
        return

    # Test image path
    test_image = "test.png"

    if not Path(test_image).exists():
        loguru.logger.error(f"Test image not found: {test_image}")
        loguru.logger.info("Available images in current directory:")
        for img_file in Path(".").glob("*.png"):
            loguru.logger.info(f"  - {img_file}")
        for img_file in Path(".").glob("*.jpg"):
            loguru.logger.info(f"  - {img_file}")
        return

    # Run inference with barcode decoding enabled
    loguru.logger.info("Running inference with barcode decoding enabled...")

    # Try different confidence thresholds to get better detections
    confidence_thresholds = [0.1, 0.15, 0.2, 0.25, 0.3]
    best_result = None
    best_decode_count = 0

    for conf_thresh in confidence_thresholds:
        loguru.logger.info(f"Trying confidence threshold: {conf_thresh}")
        result = predict_image(
            model, test_image, conf_threshold=conf_thresh, decode_barcodes=True
        )

        if result is not None and hasattr(result, "decoded_results"):
            successful_decodes = sum(
                1 for d in result.decoded_results if d["decode_success"]
            )
            loguru.logger.info(
                f"Confidence {conf_thresh}: {len(result.decoded_results)} detections, {successful_decodes} decoded"
            )

            if successful_decodes > best_decode_count:
                best_decode_count = successful_decodes
                best_result = result
                loguru.logger.info(
                    f"New best result with {successful_decodes} successful decodes"
                )

        # If we found some successful decodes, we can stop trying higher thresholds
        if best_decode_count > 0:
            break

    # Use the best result, or the last one if none had successful decodes
    result = best_result if best_result is not None else result

    if result is not None:
        # Print detection summary (includes decoded information)
        print_detection_summary(result)

        # Create visualization (includes decoded information)
        num_detections = visualize_results(result)

        loguru.logger.info(f"Inference completed! Found {num_detections} barcodes")

        # Show decoding statistics if available
        if hasattr(result, "decoded_results"):
            successful_decodes = sum(
                1 for d in result.decoded_results if d["decode_success"]
            )
            loguru.logger.info(
                f"Successfully decoded {successful_decodes}/{num_detections} barcodes"
            )

        loguru.logger.info("Check 'runs/detect/test_results' for saved results")
        loguru.logger.info(
            "Check 'test_results_visualization.jpg' for detailed visualization with decoded data"
        )


if __name__ == "__main__":
    main()
