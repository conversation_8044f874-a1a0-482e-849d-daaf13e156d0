#!/usr/bin/env python3
"""
Simple test script to verify pyzbar installation and basic functionality
"""

import cv2
import numpy as np
from pyzbar import pyzbar
import loguru


def test_pyzbar_installation():
    """Test if pyzbar is working correctly"""
    try:
        # Create a simple test image with text (not a real barcode, just for testing)
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255

        # Try to decode (should return empty list)
        result = pyzbar.decode(test_image)
        loguru.logger.info(
            f"pyzbar is working! Decoded {len(result)} objects from test image"
        )
        return True
    except Exception as e:
        loguru.logger.error(f"pyzbar test failed: {e}")
        return False


def test_with_real_image(image_path):
    """Test pyzbar with a real image"""
    try:
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            loguru.logger.error(f"Could not load image: {image_path}")
            return False

        # Convert to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Try to decode
        decoded_objects = pyzbar.decode(image_rgb)
        loguru.logger.info(f"Found {len(decoded_objects)} barcodes in full image")

        for i, obj in enumerate(decoded_objects):
            loguru.logger.info(
                f"Barcode {i+1}: Type={obj.type}, Data='{obj.data.decode('utf-8')}'"
            )

        # Also try with grayscale
        image_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        decoded_objects_gray = pyzbar.decode(image_gray)
        loguru.logger.info(
            f"Found {len(decoded_objects_gray)} barcodes in grayscale image"
        )

        return len(decoded_objects) > 0 or len(decoded_objects_gray) > 0

    except Exception as e:
        loguru.logger.error(f"Error testing with real image: {e}")
        return False


if __name__ == "__main__":
    loguru.logger.info("Testing pyzbar installation...")

    # Test basic functionality
    if test_pyzbar_installation():
        loguru.logger.info("✅ pyzbar is installed and working")
    else:
        loguru.logger.error("❌ pyzbar installation test failed")
        exit(1)

    # Test with real image
    test_image = "test2.gif"
    loguru.logger.info(f"Testing with real image: {test_image}")

    if test_with_real_image(test_image):
        loguru.logger.info("✅ Found barcodes in the test image!")
    else:
        loguru.logger.warning("⚠️ No barcodes found in the test image")
        loguru.logger.info(
            "This might be normal if the image doesn't contain readable barcodes"
        )
