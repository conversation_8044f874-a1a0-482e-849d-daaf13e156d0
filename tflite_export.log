2025-06-04 20:58:21,589 - INFO - 🚀 YOLOv8 TensorFlow Lite Export Tool (WSL Optimized)
2025-06-04 20:58:21,590 - INFO - ============================================================
2025-06-04 20:58:21,590 - INFO - 🔍 Checking environment setup...
2025-06-04 20:58:21,590 - INFO - ✅ Running in WSL environment
2025-06-04 20:58:21,590 - INFO - 🐍 Python version: 3.11.11
2025-06-04 20:58:46,017 - INFO - ✅ Ultralytics YOLOv8: 8.3.149
2025-06-04 20:59:45,401 - INFO - ✅ TensorFlow: 2.15.0
2025-06-04 20:59:54,550 - ERROR - ❌ ONNX to TensorFlow converter not installed
2025-06-04 20:59:54,550 - INFO - ✅ TensorFlow Keras: 2.15.1
2025-06-04 20:59:54,551 - INFO - ✅ NumPy: 1.26.4
2025-06-04 20:59:54,551 - ERROR - Missing dependencies: onnx2tf
2025-06-04 20:59:54,551 - ERROR - ❌ Environment check failed. Please fix the issues above.
2025-06-04 21:00:47,955 - INFO - 🚀 YOLOv8 TensorFlow Lite Export Tool (WSL Optimized)
2025-06-04 21:00:47,955 - INFO - ============================================================
2025-06-04 21:00:47,956 - INFO - 🔍 Checking environment setup...
2025-06-04 21:00:47,956 - INFO - ✅ Running in WSL environment
2025-06-04 21:00:47,956 - INFO - 🐍 Python version: 3.11.11
2025-06-04 21:00:57,951 - INFO - ✅ Ultralytics YOLOv8: 8.3.149
2025-06-04 21:01:17,482 - INFO - ✅ TensorFlow: 2.15.0
2025-06-04 21:01:20,485 - ERROR - ❌ ONNX to TensorFlow converter not installed
2025-06-04 21:01:20,485 - INFO - ✅ TensorFlow Keras: 2.15.1
2025-06-04 21:01:20,485 - INFO - ✅ NumPy: 1.26.4
2025-06-04 21:01:20,485 - ERROR - Missing dependencies: onnx2tf
2025-06-04 21:01:20,486 - ERROR - ❌ Environment check failed. Please fix the issues above.
2025-06-04 21:01:43,570 - INFO - 🚀 YOLOv8 TensorFlow Lite Export Tool (WSL Optimized)
2025-06-04 21:01:43,570 - INFO - ============================================================
2025-06-04 21:01:43,570 - INFO - 🔍 Checking environment setup...
2025-06-04 21:01:43,570 - INFO - ✅ Running in WSL environment
2025-06-04 21:01:43,570 - INFO - 🐍 Python version: 3.11.11
2025-06-04 21:01:54,078 - INFO - ✅ Ultralytics YOLOv8: 8.3.149
2025-06-04 21:02:15,554 - INFO - ✅ TensorFlow: 2.15.0
2025-06-04 21:02:18,901 - ERROR - ❌ ONNX to TensorFlow converter not installed
2025-06-04 21:02:18,902 - INFO - ✅ TensorFlow Keras: 2.15.1
2025-06-04 21:02:18,902 - INFO - ✅ NumPy: 1.26.4
2025-06-04 21:02:18,902 - ERROR - Missing dependencies: onnx2tf
2025-06-04 21:02:18,902 - ERROR - ❌ Environment check failed. Please fix the issues above.
2025-06-04 21:04:09,880 - INFO - 🚀 YOLOv8 TensorFlow Lite Export Tool (WSL Optimized)
2025-06-04 21:04:09,880 - INFO - ============================================================
2025-06-04 21:04:09,881 - INFO - 🔍 Checking environment setup...
2025-06-04 21:04:09,881 - INFO - ✅ Running in WSL environment
2025-06-04 21:04:09,881 - INFO - 🐍 Python version: 3.11.11
2025-06-04 21:04:20,223 - INFO - ✅ Ultralytics YOLOv8: 8.3.149
2025-06-04 21:04:40,684 - INFO - ✅ TensorFlow: 2.15.0
2025-06-04 21:04:43,405 - INFO - ✅ TensorFlow Keras: 2.15.1
2025-06-04 21:04:43,405 - INFO - ✅ NumPy: 1.26.4
2025-06-04 21:04:43,555 - WARNING - ⚠️  No GPU detected by TensorFlow
2025-06-04 21:04:43,555 - INFO - 🔍 Searching for trained YOLOv8 model...
2025-06-04 21:04:43,568 - INFO - ✅ Found trained model: runs/train/yolov8_barcode_detection3/weights/best.pt
2025-06-04 21:04:43,570 - INFO -    Model size: 21.5 MB
2025-06-04 21:04:43,570 - INFO - ============================================================
2025-06-04 21:04:43,571 - INFO - 📦 Loading YOLOv8 model: runs/train/yolov8_barcode_detection3/weights/best.pt
2025-06-04 21:04:45,138 - INFO - 📊 Model Information:
2025-06-04 21:04:45,139 - INFO -    Task: detect
2025-06-04 21:04:45,139 - INFO -    Model type: DetectionModel
2025-06-04 21:04:45,139 - INFO - 🚀 Starting TensorFlow Lite export...
2025-06-04 21:04:45,139 - INFO -    This may take several minutes...
2025-06-04 21:07:54,005 - INFO - 🎉 TensorFlow Lite export successful!
2025-06-04 21:07:54,005 - INFO -    📄 Output file: runs/train/yolov8_barcode_detection3/weights/best_saved_model/best_float32.tflite
2025-06-04 21:07:54,006 - INFO -    📏 File size: 42.8 MB
2025-06-04 21:07:54,006 - INFO -    ⏱️  Export time: 188.9 seconds
2025-06-04 21:07:54,006 - INFO - 🔍 Verifying TensorFlow Lite model...
2025-06-04 21:07:54,209 - INFO - ✅ TensorFlow Lite model verification successful!
2025-06-04 21:07:54,209 - INFO -    Input shape: [  1 640 640   3]
2025-06-04 21:07:54,210 - INFO -    Input dtype: <class 'numpy.float32'>
2025-06-04 21:07:54,210 - INFO -    Output shape: [   1   22 8400]
2025-06-04 21:07:54,210 - INFO -    Output dtype: <class 'numpy.float32'>
2025-06-04 21:07:54,214 - INFO - ============================================================
2025-06-04 21:07:54,214 - INFO - 🎉 Export completed successfully!
2025-06-04 21:07:54,215 - INFO - 📱 TensorFlow Lite model ready for mobile deployment: runs/train/yolov8_barcode_detection3/weights/best_saved_model/best_float32.tflite
2025-06-04 21:07:54,221 - INFO - 📖 Usage guide created: runs/train/yolov8_barcode_detection3/weights/best_saved_model/TFLITE_USAGE_GUIDE.md
2025-06-04 21:07:54,221 - INFO - ✅ All done! Check the usage guide for deployment instructions.
